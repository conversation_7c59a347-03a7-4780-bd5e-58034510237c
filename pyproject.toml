[project]
name = "dayu-fault-rag"
version = "0.1.0"
description = "RAG-based fault similarity detection system using LangGraph"
readme = "README.md"
license = { text = "MIT" }
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "langgraph>=0.2.6",
    "langchain-core>=0.3.0",
    "langchain-community>=0.3.0",
    "langchain-text-splitters>=0.3.0",
    "langchain-milvus>=0.1.0",
    "sqlalchemy>=2.0.0",
    "pymysql>=1.1.0",
    "requests>=2.31.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.1",
    "fastapi>=0.110.0",
    "uvicorn[standard]>=0.27.0",
    "streamlit>=1.30.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "jinja2>=3.1.0",
    "asyncio-throttle>=1.0.0",
    "langgraph-cli[inmem]>=0.3.4",
    "aiomysql>=0.2.0",
    "greenlet>=3.2.3",
    "pyyaml>=6.0.0",
    "aiofiles>=23.0.0",
    "httpx>=0.24.0",
    "mcp>=1.0.0",
    "langchain-openai>=0.3.28",
    "fastmcp>=2.10.6",
    "langfuse>=3.2.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.1.0",
    "ruff>=0.3.0",
    "pre-commit>=3.6.0",
    "watchdog>=4.0.0",
    "ipython>=8.20.0",
    "jupyter>=1.0.0",
]

[build-system]
requires = ["setuptools>=69.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["dayu_fault_rag"]

[tool.setuptools.package-dir]
dayu_fault_rag = "src/dayu_fault_rag"

[tool.ruff]
target-version = "py311"
line-length = 88
indent-width = 4


[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "S101"]
"docs/*" = ["D"]
"**/cli.py" = ["T20"]  # Allow print statements in CLI tools

[tool.ruff.lint.pydocstyle]
convention = "google"


[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=dayu_fault_rag",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "langsmith: marks tests that use LangSmith tracing",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__main__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.uv]
dev-dependencies = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.1.0",
    "ruff>=0.3.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
    "watchdog>=4.0.0",
    "ipython>=8.20.0",
    "jupyter>=1.0.0",
]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"
default = true

[tool.langgraph]
python_version = "3.11"
dependencies = ["."]
env = ".env"

[tool.langgraph.graphs]
fault_intelligence = "src/dayu_fault_rag/agent/graph.py:graph"
