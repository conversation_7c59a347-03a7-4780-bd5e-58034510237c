"""基于LangGraph的故障相似性检索工作流.

使用LangGraph构建现代化的RAG工作流，集成Qwen3 reranker进行结果重排序。
集成Langfuse进行可观测性和追踪。
"""

import asyncio
from typing import Any, List, Optional, Tuple

from langchain_core.documents import Document
from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langchain_milvus import Milvus
from langgraph.graph import END, START, MessagesState, StateGraph

from src.dayu_fault_rag.config.settings import SearchConfig, get_settings
from src.dayu_fault_rag.database.mysql_client import MySQLClient
from src.dayu_fault_rag.services import get_llm
from src.dayu_fault_rag.services.embedding_service import EmbeddingService
from src.dayu_fault_rag.services.rerank_service import Qwen3Rerank
from src.dayu_fault_rag.utils.logging import get_logger

logger = get_logger(__name__)


class FaultSearchState(MessagesState):
    """故障搜索工作流状态."""

    # 输入参数
    msgs: str  # 群聊天记录, 应该是一个字符串, 群聊消息
    query_text: str | None  # 处理后的群聊消息, 用于查询

    # 中间状态
    raw_results: List[Document]  # 向量召回
    reranked_results: List[Document]  # 重排序结果

    # 最终结果
    final_results: str  #
    error: str | None  # 错误信息


class FaultSearchWorkflow:
    """基于LangGraph的故障相似性检索工作流."""

    max_len = 10240

    def __init__(
        self,
        mysql_client: MySQLClient,
        reranker: Qwen3Rerank,
        milvus_client: Milvus,
        config: SearchConfig,
        llm: BaseChatModel,
        chat_prompt_template: ChatPromptTemplate,
    ):
        """初始化工作流.

        Args:
            mysql_client: MySQL数据库客户端
            milvus_client: Milvus向量数据库客户端
            reranker: 重排序器
            config: 工作流配置
            llm: LLM模型
            chat_prompt_template: 聊天prompt模板
        """
        self.mysql_client = mysql_client
        self.milvus_client = milvus_client
        self.reranker: Qwen3Rerank = reranker
        self.config: SearchConfig = config
        self.llm: BaseChatModel = llm
        self.chat_prompt_template: ChatPromptTemplate = chat_prompt_template
        # 构建LangGraph工作流
        self.graph = self._build_graph()

        logger.info("LangGraph故障搜索工作流初始化完成")

    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流."""

        # 创建状态图
        workflow = StateGraph(FaultSearchState)

        # 添加节点
        workflow.add_node("get_query_text", self._get_query_text_node)
        workflow.add_node("retrieve_similar", self._retrieve_similar_node)
        workflow.add_node("apply_rerank", self._apply_rerank_node)
        workflow.add_node("chat", self._chat_node)

        # 添加边
        workflow.add_edge(START, "get_query_text")
        workflow.add_edge("get_query_text", "retrieve_similar")
        workflow.add_edge("retrieve_similar", "apply_rerank")
        workflow.add_edge("apply_rerank", "chat")
        workflow.add_edge("chat", END)

        return workflow.compile()

    def _get_query_text_node(self, state: FaultSearchState) -> dict:
        """获取查询文本节点."""
        try:
            # 先把输入的全部聊天记录作为查询信息
            query_text = state["msgs"].strip()[: self.max_len]  # 最多给10k的中文字,
            logger.info(f"获取查询文本: {query_text}")
            return {"query_text": query_text}

        except Exception as e:
            logger.error(f"获取查询文本失败: {e}")
            return {
                "query_text": "",
                "error": str(e),
            }

    def _retrieve_similar_node(
        self, state: FaultSearchState
    ) -> dict[str, List[Document]]:
        """检索相似文档节点."""
        try:
            raw_results = []
            query = state["query_text"]
            results: List[Tuple[Document, float]] = (
                self.milvus_client.similarity_search_with_score(
                    query=query,
                    k=self.config.top_k,
                    use_cache=False,
                )
            )
            for doc, score in results:
                doc.metadata["score"] = score
                raw_results.append(doc)

            return {
                "raw_results": raw_results,
            }

        except Exception as e:
            logger.error(f"检索相似文档失败: {e}")
            return {
                "raw_results": [],
                "error": str(e),
            }

    def _apply_rerank_node(self, state: FaultSearchState) -> dict:
        """应用rerank节点."""
        try:
            raw_results: List[Document] = state["raw_results"]
            if not raw_results:
                return {
                    "reranked_results": [],
                }

            # 使用query进行rerank
            query = state["query_text"][:500]  # 限制查询长度
            ranked_docs = self.reranker.compress_documents(
                documents=raw_results, query=query
            )

            return {
                "reranked_results": ranked_docs,
            }

        except Exception as e:
            logger.warning(f"Rerank失败，使用原始结果: {e}")
            return {
                "reranked_results": state["raw_results"],
                "error": str(e),
            }

    async def _chat_node(self, state: FaultSearchState) -> dict[str, Any]:
        """使用LLM生成回复"""
        try:
            # 暂时只做单轮对话的逻辑
            reranked_results: List[Document] = state["reranked_results"]

            # 限制结果数量
            def format_doc(doc: Document) -> str:
                return f"故障ID: {doc.metadata.get('fault_id')}\n故障信息: {doc.page_content}"

            context = ""
            for doc in reranked_results:
                context += format_doc(doc)
                context += "\n -------------------------------\n"

            # 使用ChatPromptTemplate格式化消息
            messages = self.chat_prompt_template.format_messages(
                related_fault_info=context, group_msgs=state["query_text"]
            )

            response = await self.llm.ainvoke(messages)
            return {
                "final_results": response.content,
            }

        except Exception as e:
            logger.error(f"模型回答失败: {e}")
            return {
                "error": str(e),
                "final_results": [],
            }


# 便利函数
async def create_fault_search_workflow(
    mysql_client: Optional[MySQLClient] = None,
    milvus_client: Optional[Milvus] = None,
    embedding_service: Optional[EmbeddingService] = None,
    config: Optional[SearchConfig] = None,
) -> FaultSearchWorkflow:
    """创建故障搜索工作流实例.

    Args:
        mysql_client: MySQL客户端
        milvus_client: Milvus客户端
        embedding_service: 向量化服务
        config: 工作流配置

    Returns:
        故障搜索工作流实例
    """
    settings = get_settings()

    # 创建客户端
    if mysql_client is None:
        mysql_client = MySQLClient(settings.database_config)
        await mysql_client.initialize()

    if embedding_service is None:
        from src.dayu_fault_rag.services.embedding_service import get_embedding_service

        embedding_service = get_embedding_service()

    if milvus_client is None:
        from src.dayu_fault_rag.database.milvus_client import get_milvus_client

        milvus_client = get_milvus_client(
            embedding_function=embedding_service.document_embedder,
        )

    reranker = Qwen3Rerank(
        top_n=settings.search_rerank_top_n,
        url=settings.embedding_config.base_url,
    )
    search_config = settings.search_config
    llm = get_llm()
    from src.dayu_fault_rag.agent.prompts import CHAT_PROMPT_TEMPLATE

    return FaultSearchWorkflow(
        mysql_client=mysql_client,
        milvus_client=milvus_client,
        reranker=reranker,
        config=search_config,
        llm=llm,
        chat_prompt_template=CHAT_PROMPT_TEMPLATE,
    )


graph = asyncio.run(create_fault_search_workflow()).graph

if __name__ == "__main__":

    async def main():
        workflow = await create_fault_search_workflow()
        input_state = FaultSearchState(
            msgs="290254124用户反馈今天航海抽取的冰冰小灵通没有到账，麻烦核实下",
        )
        result = await workflow.graph.ainvoke(input_state)
        print(result)

    asyncio.run(main())
    # https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:8124
